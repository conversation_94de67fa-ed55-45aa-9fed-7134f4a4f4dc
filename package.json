{"name": "zhiyu<PERSON>-ai", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.12.8", "axios": "^1.6.2", "clsx": "^2.0.0", "dayjs": "^1.11.13", "lucide-react": "^0.344.0", "pdfjs-dist": "^4.0.379", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "react-window": "^1.8.8", "recharts": "^2.8.0", "tailwind-merge": "^2.2.0", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@stagewise/toolbar": "^0.4.9", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^23.0.1", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.0.4"}}