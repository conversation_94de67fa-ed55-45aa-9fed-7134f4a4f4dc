import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { BrowserRouter } from 'react-router-dom';
import ExamManagement from '../../pages/ExamManagement';

// Mock API calls
vi.mock('../../utils/api', () => ({
  examAPI: {
    getExams: vi.fn(),
    createExam: vi.fn(),
    updateExam: vi.fn(),
    deleteExam: vi.fn(),
    batchDeleteExams: vi.fn(),
    batchUpdateExamStatus: vi.fn(),
    uploadExamFile: vi.fn(),
    getAnswerSheets: vi.fn()
  }
}));

// Mock message utility
vi.mock('../../utils/message', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}));

// Mock useRealtimeStatus hook
vi.mock('../../hooks/useRealtimeStatus', () => ({
  useRealtimeStatus: vi.fn(() => ({
    status: null,
    connected: false,
    sendMessage: vi.fn()
  }))
}));

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return <BrowserRouter>{children}</BrowserRouter>;
};

describe('ExamManagement', () => {
  const mockExams = [
    {
      id: 1,
      title: '期中考试',
      description: '数学期中考试',
      status: 'active',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      answer_sheet_count: 50
    },
    {
      id: 2,
      title: '期末考试',
      description: '数学期末考试',
      status: 'draft',
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
      answer_sheet_count: 0
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock successful API responses
    const { examAPI } = require('../../utils/api');
    examAPI.getExams.mockResolvedValue({ data: mockExams });
    examAPI.createExam.mockResolvedValue({ data: { id: 3, title: '新考试' } });
    examAPI.updateExam.mockResolvedValue({ data: { id: 1, title: '更新的考试' } });
    examAPI.deleteExam.mockResolvedValue({ data: { success: true } });
    examAPI.batchDeleteExams.mockResolvedValue({ data: { success: true } });
    examAPI.batchUpdateExamStatus.mockResolvedValue({ data: { success: true } });
    examAPI.uploadExamFile.mockResolvedValue({ data: { success: true } });
    examAPI.getAnswerSheets.mockResolvedValue({ data: [] });
  });

  it('should render exam management page', async () => {
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    // 检查页面标题
    expect(screen.getByText('考试管理')).toBeInTheDocument();
    
    // 检查创建考试按钮
    expect(screen.getByText('创建考试')).toBeInTheDocument();
    
    // 等待考试列表加载
    await waitFor(() => {
      expect(screen.getByText('期中考试')).toBeInTheDocument();
      expect(screen.getByText('期末考试')).toBeInTheDocument();
    });
  });

  it('should display exam list with correct information', async () => {
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    await waitFor(() => {
      // 检查考试标题
      expect(screen.getByText('期中考试')).toBeInTheDocument();
      expect(screen.getByText('期末考试')).toBeInTheDocument();
      
      // 检查考试描述
      expect(screen.getByText('数学期中考试')).toBeInTheDocument();
      expect(screen.getByText('数学期末考试')).toBeInTheDocument();
      
      // 检查考试状态
      expect(screen.getByText('进行中')).toBeInTheDocument();
      expect(screen.getByText('草稿')).toBeInTheDocument();
      
      // 检查答题卡数量
      expect(screen.getByText('50')).toBeInTheDocument();
      expect(screen.getByText('0')).toBeInTheDocument();
    });
  });

  it('should open create exam modal when create button is clicked', async () => {
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    // 点击创建考试按钮
    const createButton = screen.getByText('创建考试');
    fireEvent.click(createButton);

    // 检查模态框是否打开
    await waitFor(() => {
      expect(screen.getByText('创建新考试')).toBeInTheDocument();
      expect(screen.getByLabelText('考试标题')).toBeInTheDocument();
      expect(screen.getByLabelText('考试描述')).toBeInTheDocument();
    });
  });

  it('should create new exam successfully', async () => {
    const { message } = require('../../utils/message');
    
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    // 打开创建考试模态框
    fireEvent.click(screen.getByText('创建考试'));

    await waitFor(() => {
      expect(screen.getByText('创建新考试')).toBeInTheDocument();
    });

    // 填写表单
    const titleInput = screen.getByLabelText('考试标题');
    const descriptionInput = screen.getByLabelText('考试描述');
    
    fireEvent.change(titleInput, { target: { value: '新考试' } });
    fireEvent.change(descriptionInput, { target: { value: '新考试描述' } });

    // 提交表单
    const submitButton = screen.getByText('创建');
    fireEvent.click(submitButton);

    // 验证API调用
    await waitFor(() => {
      const { examAPI } = require('../../utils/api');
      expect(examAPI.createExam).toHaveBeenCalledWith({
        title: '新考试',
        description: '新考试描述'
      });
      expect(message.success).toHaveBeenCalledWith('考试创建成功');
    });
  });

  it('should handle exam deletion', async () => {
    const { message } = require('../../utils/message');
    
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('期中考试')).toBeInTheDocument();
    });

    // 找到删除按钮并点击
    const deleteButtons = screen.getAllByText('删除');
    fireEvent.click(deleteButtons[0]);

    // 确认删除
    await waitFor(() => {
      expect(screen.getByText('确认删除')).toBeInTheDocument();
    });
    
    const confirmButton = screen.getByText('确定');
    fireEvent.click(confirmButton);

    // 验证API调用
    await waitFor(() => {
      const { examAPI } = require('../../utils/api');
      expect(examAPI.deleteExam).toHaveBeenCalledWith(1);
      expect(message.success).toHaveBeenCalledWith('考试删除成功');
    });
  });

  it('should handle batch operations', async () => {
    const { message } = require('../../utils/message');
    
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('期中考试')).toBeInTheDocument();
    });

    // 选择多个考试
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[1]); // 选择第一个考试
    fireEvent.click(checkboxes[2]); // 选择第二个考试

    // 批量删除
    const batchDeleteButton = screen.getByText('批量删除');
    fireEvent.click(batchDeleteButton);

    // 确认删除
    await waitFor(() => {
      expect(screen.getByText('确认批量删除')).toBeInTheDocument();
    });
    
    const confirmButton = screen.getByText('确定');
    fireEvent.click(confirmButton);

    // 验证API调用
    await waitFor(() => {
      const { examAPI } = require('../../utils/api');
      expect(examAPI.batchDeleteExams).toHaveBeenCalledWith([1, 2]);
      expect(message.success).toHaveBeenCalledWith('批量删除成功');
    });
  });

  it('should handle batch status update', async () => {
    const { message } = require('../../utils/message');
    
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('期中考试')).toBeInTheDocument();
    });

    // 选择多个考试
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[1]); // 选择第一个考试
    fireEvent.click(checkboxes[2]); // 选择第二个考试

    // 批量更新状态
    const statusSelect = screen.getByDisplayValue('批量操作');
    fireEvent.change(statusSelect, { target: { value: 'active' } });

    const updateButton = screen.getByText('更新状态');
    fireEvent.click(updateButton);

    // 验证API调用
    await waitFor(() => {
      const { examAPI } = require('../../utils/api');
      expect(examAPI.batchUpdateExamStatus).toHaveBeenCalledWith({
        exam_ids: [1, 2],
        status: 'active'
      });
      expect(message.success).toHaveBeenCalledWith('批量状态更新成功');
    });
  });

  it('should handle file upload', async () => {
    const { message } = require('../../utils/message');
    
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('期中考试')).toBeInTheDocument();
    });

    // 找到上传按钮
    const uploadButtons = screen.getAllByText('上传试卷');
    fireEvent.click(uploadButtons[0]);

    // 模拟文件选择
    const fileInput = screen.getByLabelText('选择文件');
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    });
    
    fireEvent.change(fileInput);

    // 点击上传按钮
    const uploadButton = screen.getByText('开始上传');
    fireEvent.click(uploadButton);

    // 验证API调用
    await waitFor(() => {
      const { examAPI } = require('../../utils/api');
      expect(examAPI.uploadExamFile).toHaveBeenCalled();
      expect(message.success).toHaveBeenCalledWith('文件上传成功');
    });
  });

  it('should filter exams by status', async () => {
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('期中考试')).toBeInTheDocument();
      expect(screen.getByText('期末考试')).toBeInTheDocument();
    });

    // 筛选活跃状态的考试
    const statusFilter = screen.getByDisplayValue('全部状态');
    fireEvent.change(statusFilter, { target: { value: 'active' } });

    // 验证只显示活跃状态的考试
    await waitFor(() => {
      expect(screen.getByText('期中考试')).toBeInTheDocument();
      expect(screen.queryByText('期末考试')).not.toBeInTheDocument();
    });
  });

  it('should search exams by title', async () => {
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('期中考试')).toBeInTheDocument();
      expect(screen.getByText('期末考试')).toBeInTheDocument();
    });

    // 搜索考试
    const searchInput = screen.getByPlaceholderText('搜索考试标题');
    fireEvent.change(searchInput, { target: { value: '期中' } });

    // 验证搜索结果
    await waitFor(() => {
      expect(screen.getByText('期中考试')).toBeInTheDocument();
      expect(screen.queryByText('期末考试')).not.toBeInTheDocument();
    });
  });

  it('should handle API errors gracefully', async () => {
    const { message } = require('../../utils/message');
    const { examAPI } = require('../../utils/api');
    
    // Mock API error
    examAPI.getExams.mockRejectedValue(new Error('网络错误'));
    
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    // 验证错误处理
    await waitFor(() => {
      expect(message.error).toHaveBeenCalledWith('获取考试列表失败: 网络错误');
    });
  });

  it('should display loading state', async () => {
    const { examAPI } = require('../../utils/api');
    
    // Mock delayed API response
    examAPI.getExams.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({ data: mockExams }), 1000))
    );
    
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    // 检查加载状态
    expect(screen.getByText('加载中...')).toBeInTheDocument();
  });

  it('should display empty state when no exams', async () => {
    const { examAPI } = require('../../utils/api');
    examAPI.getExams.mockResolvedValue({ data: [] });
    
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    // 检查空状态
    await waitFor(() => {
      expect(screen.getByText('暂无考试数据')).toBeInTheDocument();
    });
  });

  it('should handle exam editing', async () => {
    const { message } = require('../../utils/message');
    
    render(
      <TestWrapper>
        <ExamManagement />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('期中考试')).toBeInTheDocument();
    });

    // 找到编辑按钮并点击
    const editButtons = screen.getAllByText('编辑');
    fireEvent.click(editButtons[0]);

    // 检查编辑模态框
    await waitFor(() => {
      expect(screen.getByText('编辑考试')).toBeInTheDocument();
    });

    // 修改标题
    const titleInput = screen.getByDisplayValue('期中考试');
    fireEvent.change(titleInput, { target: { value: '更新的期中考试' } });

    // 保存修改
    const saveButton = screen.getByText('保存');
    fireEvent.click(saveButton);

    // 验证API调用
    await waitFor(() => {
      const { examAPI } = require('../../utils/api');
      expect(examAPI.updateExam).toHaveBeenCalledWith(1, {
        title: '更新的期中考试',
        description: '数学期中考试'
      });
      expect(message.success).toHaveBeenCalledWith('考试更新成功');
    });
  });
});