import { renderHook, act, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useRealtimeStatus } from '../../hooks/useRealtimeStatus';

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;

  readyState: number = MockWebSocket.CONNECTING;
  url: string;
  onopen: ((event: Event) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;

  constructor(url: string) {
    this.url = url;
    // 模拟异步连接
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN;
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 10);
  }

  send(data: string) {
    if (this.readyState !== MockWebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }
  }

  close(code?: number, reason?: string) {
    this.readyState = MockWebSocket.CLOSING;
    setTimeout(() => {
      this.readyState = MockWebSocket.CLOSED;
      if (this.onclose) {
        this.onclose(new CloseEvent('close', { code: code || 1000, reason: reason || '' }));
      }
    }, 10);
  }

  simulateMessage(data: any) {
    if (this.onmessage) {
      this.onmessage(new MessageEvent('message', { data: JSON.stringify(data) }));
    }
  }

  simulateError() {
    if (this.onerror) {
      this.onerror(new Event('error'));
    }
  }
}

// Mock global WebSocket
Object.defineProperty(window, 'WebSocket', {
  writable: true,
  value: MockWebSocket,
});

describe('useRealtimeStatus', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useRealtimeStatus('test-exam-id'));

    expect(result.current.status).toBeNull();
    expect(result.current.connected).toBe(false);
    expect(typeof result.current.sendMessage).toBe('function');
  });

  it('should connect to WebSocket on mount', async () => {
    const { result } = renderHook(() => useRealtimeStatus('test-exam-id'));

    // 等待连接建立
    await waitFor(() => {
      expect(result.current.connected).toBe(true);
    });

    expect(result.current.connected).toBe(true);
  });

  it('should receive and process status update messages', async () => {
    const { result } = renderHook(() => useRealtimeStatus('test-exam-id'));

    // 等待连接建立
    await waitFor(() => {
      expect(result.current.connected).toBe(true);
    });

    // 模拟接收状态更新消息
    const statusUpdateMessage = {
      type: 'status_update',
      payload: {
        examId: 'test-exam-id',
        totalSheets: 100,
        processedSheets: 50,
        currentStatus: 'processing',
        errors: []
      }
    };

    // 获取WebSocket实例并模拟消息
    const wsConstructor = window.WebSocket as any;
    const wsInstance = new wsConstructor('test-url') as MockWebSocket;
    
    act(() => {
      wsInstance.simulateMessage(statusUpdateMessage);
    });

    await waitFor(() => {
      expect(result.current.status).toEqual(statusUpdateMessage.payload);
    });
  });

  it('should handle processing completion messages', async () => {
    const { result } = renderHook(() => useRealtimeStatus('test-exam-id'));

    // 等待连接建立
    await waitFor(() => {
      expect(result.current.connected).toBe(true);
    });

    // 模拟处理完成消息
    const completionMessage = {
      type: 'processing_complete'
    };

    const wsConstructor = window.WebSocket as any;
    const wsInstance = new wsConstructor('test-url') as MockWebSocket;
    
    act(() => {
      wsInstance.simulateMessage(completionMessage);
    });

    // 处理完成消息不会更新status，但会触发成功提示
    expect(result.current.connected).toBe(true);
  });

  it('should handle error messages', async () => {
    const { result } = renderHook(() => useRealtimeStatus('test-exam-id'));

    // 等待连接建立
    await waitFor(() => {
      expect(result.current.connected).toBe(true);
    });

    // 模拟错误消息
    const errorMessage = {
      type: 'error',
      message: 'OCR processing failed'
    };

    const wsConstructor = window.WebSocket as any;
    const wsInstance = new wsConstructor('test-url') as MockWebSocket;
    
    act(() => {
      wsInstance.simulateMessage(errorMessage);
    });

    // 错误消息会触发错误提示，但不会更新status
    expect(result.current.connected).toBe(true);
  });

  it('should handle grading completion messages', async () => {
    const { result } = renderHook(() => useRealtimeStatus('test-exam-id'));

    // 等待连接建立
    await waitFor(() => {
      expect(result.current.connected).toBe(true);
    });

    // 模拟阅卷完成消息
    const gradingCompleteMessage = {
      type: 'grading_complete'
    };

    const wsConstructor = window.WebSocket as any;
    const wsInstance = new wsConstructor('test-url') as MockWebSocket;
    
    act(() => {
      wsInstance.simulateMessage(gradingCompleteMessage);
    });

    // 阅卷完成消息会触发成功提示
    expect(result.current.connected).toBe(true);
  });

  it('should handle malformed JSON messages gracefully', async () => {
    const { result } = renderHook(() => useRealtimeStatus('test-exam-id'));

    // 等待连接建立
    await waitFor(() => {
      expect(result.current.connected).toBe(true);
    });

    const wsConstructor = window.WebSocket as any;
    const wsInstance = new wsConstructor('test-url') as MockWebSocket;
    
    // 模拟接收格式错误的消息
    act(() => {
      if (wsInstance.onmessage) {
        wsInstance.onmessage(new MessageEvent('message', { data: 'invalid json' }));
      }
    });

    // 连接应该保持正常，状态不应该改变
    expect(result.current.connected).toBe(true);
    expect(result.current.status).toBeNull();
  });

  it('should send messages through WebSocket', async () => {
    const { result } = renderHook(() => useRealtimeStatus('test-exam-id'));

    // 等待连接建立
    await waitFor(() => {
      expect(result.current.connected).toBe(true);
    });

    // 模拟发送消息
    const testMessage = {
      type: 'subscribe',
      topic: 'exam_updates'
    };

    act(() => {
      result.current.sendMessage(testMessage);
    });

    // 验证消息发送没有错误
    expect(result.current.connected).toBe(true);
  });

  it('should handle connection close', async () => {
    const { result } = renderHook(() => useRealtimeStatus('test-exam-id'));

    // 等待连接建立
    await waitFor(() => {
      expect(result.current.connected).toBe(true);
    });

    const wsConstructor = window.WebSocket as any;
    const wsInstance = new wsConstructor('test-url') as MockWebSocket;
    
    // 模拟连接关闭
    act(() => {
      wsInstance.close();
    });

    await waitFor(() => {
      expect(result.current.connected).toBe(false);
    });
  });

  it('should cleanup on unmount', async () => {
    const { result, unmount } = renderHook(() => useRealtimeStatus('test-exam-id'));

    // 等待连接建立
    await waitFor(() => {
      expect(result.current.connected).toBe(true);
    });

    // 卸载组件
    unmount();

    // 验证清理逻辑
    expect(result.current.connected).toBe(true); // 在卸载时状态保持最后的值
  });

  it('should not connect when examId is empty', () => {
    const { result } = renderHook(() => useRealtimeStatus(''));

    expect(result.current.status).toBeNull();
    expect(result.current.connected).toBe(false);
  });
});